#!/usr/bin/env node

/**
 * Test script to verify OAuth authentication flow
 */

const axios = require('axios');

async function testOAuthFlow() {
  console.log('🔐 Testing OAuth Authentication Flow...\n');

  const backendUrl = 'http://localhost:5001';
  const projectId = 'project-drishti-467114';

  try {
    // Step 1: Test backend health
    console.log('1. Testing backend health...');
    const healthResponse = await axios.get(`${backendUrl}/health`);
    console.log('   ✅ Backend is healthy:', healthResponse.data);

    // Step 2: Get OAuth token
    console.log('\n2. Requesting OAuth token...');
    const tokenResponse = await axios.post(`${backendUrl}/api/auth/vertex-ai-token`, {
      projectId: projectId
    });
    
    const { access_token, expires_in, token_type } = tokenResponse.data;
    console.log('   ✅ OAuth token received');
    console.log(`   🔑 Token type: ${token_type}`);
    console.log(`   ⏰ Expires in: ${expires_in} seconds`);
    console.log(`   📝 Token preview: ${access_token.substring(0, 50)}...`);

    // Step 3: Test detection endpoint (with dummy image)
    console.log('\n3. Testing detection endpoint...');
    
    // Create a small dummy base64 image (1x1 pixel PNG)
    const dummyImage = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    
    try {
      const detectionResponse = await axios.post(`${backendUrl}/api/vertex-ai/detect`, {
        base64Image: dummyImage,
        projectId: projectId,
        location: 'us-central1'
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`
        }
      });

      console.log('   ✅ Detection endpoint working');
      console.log('   📊 Response:', {
        total_people: detectionResponse.data.total_people,
        status: detectionResponse.data.status,
        detections_count: detectionResponse.data.detections?.length || 0
      });

    } catch (detectionError) {
      if (detectionError.response?.status === 400) {
        console.log('   ⚠️  Detection failed (expected with dummy image)');
        console.log('   📝 Error:', detectionError.response.data.error);
      } else {
        console.log('   ❌ Detection endpoint error:', detectionError.message);
      }
    }

    console.log('\n🎉 OAuth Flow Test Complete!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Backend is running and healthy');
    console.log('   ✅ OAuth token generation working');
    console.log('   ✅ Detection endpoint accessible');
    console.log('\n🚀 Ready to test with frontend!');
    console.log('   Run: npm start (in main directory)');
    console.log('   Visit: http://localhost:3000');

  } catch (error) {
    console.error('❌ OAuth flow test failed:', error.message);
    
    if (error.response) {
      console.error('   📝 Response data:', error.response.data);
      console.error('   📊 Status code:', error.response.status);
    }
    
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Ensure backend is running: cd backend && npm start');
    console.log('   2. Check service account key exists: backend/project-drishti-467114-*.json');
    console.log('   3. Verify project ID in .env files');
  }
}

if (require.main === module) {
  testOAuthFlow().catch(console.error);
}

module.exports = { testOAuthFlow };
