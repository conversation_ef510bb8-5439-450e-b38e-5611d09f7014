#!/usr/bin/env node

/**
 * Test script to verify Vertex AI authentication setup
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

async function testSetup() {
  console.log('🔍 Testing Drishti Vertex AI Setup...\n');

  // Test 1: Check if backend dependencies are installed
  console.log('1. Checking backend dependencies...');
  const backendPackageJson = path.join(__dirname, 'backend', 'package.json');
  const nodeModules = path.join(__dirname, 'backend', 'node_modules');
  
  if (fs.existsSync(backendPackageJson) && fs.existsSync(nodeModules)) {
    console.log('   ✅ Backend dependencies installed');
  } else {
    console.log('   ❌ Backend dependencies missing. Run: cd backend && npm install');
    return;
  }

  // Test 2: Check environment configuration
  console.log('\n2. Checking environment configuration...');
  const frontendEnv = path.join(__dirname, '.env');
  const backendEnv = path.join(__dirname, 'backend', '.env');
  
  if (fs.existsSync(frontendEnv)) {
    console.log('   ✅ Frontend .env file exists');
  } else {
    console.log('   ❌ Frontend .env file missing');
  }
  
  if (fs.existsSync(backendEnv)) {
    console.log('   ✅ Backend .env file exists');
  } else {
    console.log('   ❌ Backend .env file missing');
  }

  // Test 3: Check service account key
  console.log('\n3. Checking service account key...');
  const serviceAccountKey = path.join(__dirname, 'backend', 'service-account-key.json');
  
  if (fs.existsSync(serviceAccountKey)) {
    console.log('   ✅ Service account key file exists');
    
    try {
      const keyContent = JSON.parse(fs.readFileSync(serviceAccountKey, 'utf8'));
      if (keyContent.type === 'service_account' && keyContent.project_id) {
        console.log(`   ✅ Valid service account for project: ${keyContent.project_id}`);
      } else {
        console.log('   ⚠️  Service account key format may be invalid');
      }
    } catch (error) {
      console.log('   ⚠️  Could not parse service account key');
    }
  } else {
    console.log('   ❌ Service account key missing');
    console.log('      Download from Google Cloud Console and save as backend/service-account-key.json');
  }

  // Test 4: Try to start backend (if not already running)
  console.log('\n4. Testing backend connection...');
  try {
    const response = await axios.get('http://localhost:5001/health', { timeout: 2000 });
    console.log('   ✅ Backend is running and responding');
    console.log(`   📡 Backend response: ${JSON.stringify(response.data)}`);
  } catch (error) {
    console.log('   ⚠️  Backend not responding. Start with: cd backend && npm start');
    console.log(`   🔍 Error: ${error.message}`);
  }

  console.log('\n📋 Setup Summary:');
  console.log('   1. Install backend dependencies: cd backend && npm install');
  console.log('   2. Download service account key to backend/service-account-key.json');
  console.log('   3. Start backend: cd backend && npm start');
  console.log('   4. Start frontend: npm start');
  console.log('   5. Test at http://localhost:3000');
  
  console.log('\n📖 For detailed instructions, see: setup-vertex-ai.md');
}

// Add health endpoint to backend server for testing
const healthEndpoint = `
// Add this to your backend/server.js file:
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'drishti-backend'
  });
});
`;

if (require.main === module) {
  testSetup().catch(console.error);
}

module.exports = { testSetup };
