# Google Maps API Key
# Get your API key from: https://console.cloud.google.com/google/maps-apis/
# Make sure to enable the following APIs:
# - Maps JavaScript API
# - Places API (optional)
# - Geocoding API (optional)
REACT_APP_GOOGLE_MAPS_API_KEY=AIzaSyBqvZbeCQTuBNVqD6DtpiLY_mlecmF8HYE

REACT_APP_GOOGLE_CLOUD_PROJECT_ID=project-drishti-467114
REACT_APP_GOOGLE_CLOUD_API_KEY=AIzaSyDV0NYeGzHQWH9D0dnL_l6GiJMBGGHCPuo
REACT_APP_VERTEX_LOCATION=us-central1
REACT_APP_VERTEX_API_ENDPOINT=https://us-central1-aiplatform.googleapis.com


# Firebase Configuration (already configured in firebase.js)
# These are already set up in your firebase.js file
REACT_APP_FIREBASE_API_KEY=your_firebase_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=project-drishti-467114.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=project-drishti-467114
REACT_APP_FIREBASE_STORAGE_BUCKET=project-drishti-467114.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
REACT_APP_FIREBASE_APP_ID=zero2agent-ffe2e
