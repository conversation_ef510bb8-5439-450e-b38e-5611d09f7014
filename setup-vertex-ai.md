# Vertex AI Authentication Setup Guide

## The Problem
You're getting a 401 authentication error because Vertex AI doesn't accept API keys. It requires OAuth 2.0 access tokens or service account authentication.

## Solution: Use Backend Proxy (Recommended)

### Step 1: Create Service Account
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **IAM & Admin > Service Accounts**
3. Click **"Create Service Account"**
4. Name: `drishti-vertex-ai`
5. Add these roles:
   - `Vertex AI User`
   - `AI Platform Developer`
6. Click **"Create and Continue"**
7. Click **"Create Key"** → **JSON** → **Create**
8. Download the JSON file and save it as `backend/service-account-key.json`

### Step 2: Install Backend Dependencies
```bash
cd backend
npm install
```

### Step 3: Start Backend Server
```bash
cd backend
npm start
```

The backend should start on http://localhost:5001

### Step 4: Start Frontend
```bash
# In the main project directory
npm start
```

### Step 5: Test the Setup
1. Open http://localhost:3000
2. Click "📹 Show Video"
3. Grant camera permissions
4. Click "Start AI" button
5. Check browser console for authentication messages

## Expected Behavior

### ✅ Success Messages:
- "Backend authentication successful"
- "Vertex AI detected X people"

### ⚠️ Fallback Messages:
- "Using mock detection - configure backend authentication for real detection"
- "Backend Vertex AI detection failed, using mock detection"

## Troubleshooting

### Backend Won't Start
```bash
cd backend
npm install
# Check if service account file exists
ls -la service-account-key.json
```

### Authentication Still Failing
1. Verify service account has correct roles
2. Check project ID matches in both `.env` files
3. Ensure backend is running on port 5001
4. Check browser network tab for API calls

### Cost Concerns
- Vertex AI Vision API: ~$1.50 per 1,000 images
- Mock detection runs automatically if authentication fails
- Monitor usage in Google Cloud Console

## Files Modified
- ✅ `backend/.env` - Created with proper configuration
- ✅ `.env` - Updated to use backend proxy
- ✅ `src/services/videoProcessingService.js` - Updated to use backend

## Next Steps
1. Download service account key to `backend/service-account-key.json`
2. Start backend: `cd backend && npm start`
3. Start frontend: `npm start`
4. Test the video detection feature
